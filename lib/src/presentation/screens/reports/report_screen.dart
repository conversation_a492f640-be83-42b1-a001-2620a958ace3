import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:neorevv/src/core/config/app_strings.dart';
import 'package:neorevv/src/core/config/constants.dart';
import 'package:neorevv/src/core/config/json_consts.dart';
import 'package:neorevv/src/core/config/responsive.dart';
import 'package:neorevv/src/core/theme/app_fonts.dart';
import 'package:neorevv/src/core/theme/app_theme.dart';
import 'package:neorevv/src/domain/models/report.dart';
import '../dashboard/components/header.dart';
import 'components/report_sidebar.dart';
import 'components/pdf_preview_widget.dart';

class ReportScreen extends HookWidget {
  const ReportScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final header = Header(selectedTab: reportsTab);
    final selectedReport = useState<ReportModel?>(null);
    final reports = useState<List<ReportModel>>(reportsListData);

    // Set initial selected report
    useEffect(() {
      selectedReport.value = reports.value.firstWhere((r) => r.isSelected);
      return null;
    }, []);

    void onReportSelected(ReportModel report) {
      // Update selection state
      final updatedReports = reports.value.map((r) {
        return r.copyWith(isSelected: r.id == report.id);
      }).toList();

      reports.value = updatedReports;
      selectedReport.value = report;
    }

    final sidebarWidth = ResponsiveSizes.reportSidebarWidth(context);
    final sidebarHeight = ResponsiveSizes.reportSidebarHeight(context);
    final pdfPanelHeight = ResponsiveSizes.pdfPreviewPanelHeight(context);

    return Scaffold(
      drawer: header.mobileDrawer,
      body: SafeArea(
        child: Container(
          padding: EdgeInsets.fromLTRB(
            Responsive.isMobile(context) ? 8 : webLayoutmargin,
            Responsive.isMobile(context) ? 8 : defaultMargin,
            Responsive.isMobile(context) ? 8 : webLayoutmargin,
            0,
          ),
          child: Column(
            children: [
              header,
              const SizedBox(height: defaultPadding * 2.5),

              // Reports Heading
              Container(
                width: double.infinity,
                padding: const EdgeInsets.only(bottom: defaultPadding),
                child: Row(
                  children: [
                    Image.asset(
                      '$iconAssetpath/fi-rr-document.png',
                      width: 20,
                      height: 20,
                      color: AppTheme.black,
                    ),
                    const SizedBox(width: 4),
                    Flexible(
                      child: Text(
                        'Reports',
                        style: AppFonts.semiBoldTextStyle(
                          22,
                          color: AppTheme.black,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // Main Content Row
              Expanded(
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Left Panel - Report Sidebar with background
                    Container(
                      width: sidebarWidth,
                      height: sidebarHeight,
                      padding: const EdgeInsets.all(defaultPadding),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        color: Colors.white,
                      ),
                      child: ReportSidebar(
                        reports: reports.value,
                        onReportSelected: onReportSelected,
                      ),
                    ),

                    const SizedBox(width: defaultPadding),

                    // Right Panel - PDF Preview (Viewer + Info combined)
                    Expanded(
                      child: SizedBox(
                        height: pdfPanelHeight,
                        child: selectedReport.value != null
                            ? PdfPreviewWidget(report: selectedReport.value!)
                            : Container(
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(12),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.grey.withValues(alpha: 0.1),
                                      spreadRadius: 1,
                                      blurRadius: 8,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: const Center(
                                  child: Text('Select a report to preview'),
                                ),
                              ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
