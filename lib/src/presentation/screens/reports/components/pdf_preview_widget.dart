import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:neorevv/src/core/config/app_strings.dart';
import 'package:neorevv/src/core/config/constants.dart';
import 'package:neorevv/src/core/theme/app_fonts.dart';
import 'package:neorevv/src/core/theme/app_theme.dart';
import 'package:neorevv/src/domain/models/report.dart';
import 'package:dio/dio.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'dart:async';

// Web-specific imports (conditional)
// ignore: avoid_web_libraries_in_flutter, deprecated_member_use
import 'dart:html' as html;
// ignore: avoid_web_libraries_in_flutter
import 'dart:ui_web' as ui_web;

class PdfPreviewWidget extends StatefulWidget {
  final ReportModel report;

  const PdfPreviewWidget({super.key, required this.report});

  @override
  State<PdfPreviewWidget> createState() => _PdfPreviewWidgetState();
}

class _PdfPreviewWidgetState extends State<PdfPreviewWidget> {
  bool isPdfLoaded = false;

  // PDF loading state
  bool isLoadingPdf = false;
  bool hasError = false;
  String errorMessage = '';
  Uint8List? pdfBytes;
  String? pdfFilePath;

  @override
  void initState() {
    super.initState();
    _loadPdf();
  }

  Future<void> _loadPdf() async {
    setState(() {
      isLoadingPdf = true;
      hasError = false;
      errorMessage = '';
      isPdfLoaded = false;
    });

    try {
      if (widget.report.url.isNotEmpty) {
        await _loadRemotePdf();
      } else {
        await _loadLocalPdf();
      }
      setState(() {
        isPdfLoaded = true;
      });
    } catch (e) {
      debugPrint('Error loading PDF: $e');
      setState(() {
        isLoadingPdf = false;
        hasError = true;
        errorMessage = 'Failed to load PDF: $e';
        isPdfLoaded = false;
      });
    }
  }

  Future<void> _loadRemotePdf() async {
    try {
      debugPrint('Loading PDF from URL: ${widget.report.url}');
      final dio = Dio();
      final response = await dio.get(
        widget.report.url,
        options: Options(responseType: ResponseType.bytes),
      );

      if (response.statusCode == 200 && response.data != null) {
        final bytes = Uint8List.fromList(response.data);

        // For mobile, save to temporary file
        if (!kIsWeb) {
          await _savePdfToTempFile(bytes);
        }

        setState(() {
          pdfBytes = bytes;
          isLoadingPdf = false;
        });
        debugPrint('PDF loaded successfully: ${pdfBytes!.length} bytes');
      } else {
        throw Exception('Failed to load PDF: HTTP ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error loading remote PDF: $e');
      // Fallback to local PDF
      await _loadLocalPdf();
    }
  }

  Future<void> _loadLocalPdf() async {
    try {
      debugPrint('Loading local PDF asset...');
      final ByteData data = await rootBundle.load(
        'assets/pdfs/sample_report.pdf',
      );
      final bytes = data.buffer.asUint8List();

      // For mobile, we need to save to a temporary file
      if (!kIsWeb) {
        await _savePdfToTempFile(bytes);
      }

      setState(() {
        pdfBytes = bytes;
        isLoadingPdf = false;
        hasError = false;
      });
      debugPrint('Local PDF loaded successfully: ${pdfBytes!.length} bytes');
    } catch (e) {
      debugPrint('Error loading local PDF: $e');
      setState(() {
        isLoadingPdf = false;
        hasError = true;
        errorMessage =
            'Failed to load PDF. Both remote and local sources unavailable.';
      });
    }
  }

  Future<void> _savePdfToTempFile(Uint8List bytes) async {
    try {
      // For mobile platforms, we need to save the PDF to a temporary file
      // This is a simplified approach - in production you'd use path_provider
      final tempDir =
          '/tmp'; // This would be replaced with proper temp directory
      final fileName =
          'temp_${widget.report.id}_${DateTime.now().millisecondsSinceEpoch}.pdf';
      pdfFilePath = '$tempDir/$fileName';

      debugPrint('PDF would be saved to: $pdfFilePath');
    } catch (e) {
      debugPrint('Error saving PDF to temp file: $e');
    }
  }

  void _handleDownload() {
    if (!isPdfLoaded) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.download, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'Downloading ${widget.report.title}.pdf...',
                style: AppFonts.regularTextStyle(14, color: Colors.white),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
        action: SnackBarAction(
          label: 'View',
          textColor: Colors.white,
          onPressed: () {
            // Open file location or viewer
          },
        ),
      ),
    );
  }

  Widget _buildWebPdfViewer() {
    if (pdfBytes == null) return const SizedBox();

    return _EmbeddedPDFViewerWeb(
      fileBytes: pdfBytes!,
      fileName: widget.report.title,
    );
  }

  Widget _buildMobilePdfViewer() {
    if (pdfBytes == null || pdfFilePath == null) {
      return Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.grey[100],
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.picture_as_pdf, size: 64, color: Colors.red[400]),
              const SizedBox(height: 16),
              Text(
                'PDF Viewer',
                style: AppFonts.semiBoldTextStyle(18, color: Colors.grey[700]),
              ),
              const SizedBox(height: 8),
              Text(
                'Preparing PDF for mobile viewing...',
                style: AppFonts.regularTextStyle(14, color: Colors.grey[600]),
              ),
            ],
          ),
        ),
      );
    }

    return _EmbeddedPDFViewerMobile(
      filePath: pdfFilePath!,
      fileName: widget.report.title,
    );
  }

  Widget _buildPdfInfoPanelContent() {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.reportBg,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // PDF Icon and Title Section
          Container(
            padding: const EdgeInsets.only(
              top: defaultPadding * 2,
              left: defaultPadding,
              right: defaultPadding,
              bottom: 0,
            ),
            // padding: const EdgeInsets.all(defaultPadding),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // PDF Icon
                SizedBox(
                  width: 16 * 2 * 1.4,
                  height: 16 * 2 * 1.4,
                  child: Image.asset('$iconAssetpath/files_icons.png'),
                ),
                const SizedBox(width: 12),

                // PDF Title
                Expanded(
                  child: Text(
                    '${widget.report.title}.pdf',
                    style:
                        AppFonts.semiBoldTextStyle(
                          16,
                          color: AppTheme.primaryTextColor,
                        ).copyWith(
                          height: 1.5, // Line height multiplier
                        ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),

          // Action Buttons Section
          Container(
            padding: const EdgeInsets.all(defaultPadding),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(width: 12),
                // Download Button
                SizedBox(
                  width: ResponsiveSizes.applyButtonWidth(context),
                  height: 45,
                  child: ElevatedButton(
                    onPressed: isPdfLoaded ? _handleDownload : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.downloadBtn,
                      foregroundColor: Colors.black,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(30),
                      ),
                      elevation: 0,
                    ),
                    child: Text(
                      download,
                      style: AppFonts.mediumTextStyle(14, color: Colors.black),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPdfViewerContent() {
    if (isLoadingPdf) {
      return SizedBox(
        width: double.infinity,
        height: double.infinity,
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Loading PDF...'),
            ],
          ),
        ),
      );
    }

    if (hasError) {
      return SizedBox(
        width: double.infinity,
        height: double.infinity,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline, size: 64, color: Colors.red[400]),
              const SizedBox(height: 16),
              Text(
                'Error Loading PDF',
                style: AppFonts.semiBoldTextStyle(
                  18,
                  color: AppTheme.primaryTextColor,
                ),
              ),
              const SizedBox(height: 8),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 32),
                child: Text(
                  errorMessage,
                  textAlign: TextAlign.center,
                  style: AppFonts.regularTextStyle(14, color: Colors.grey[600]),
                ),
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _loadPdf,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                ),
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    if (pdfBytes != null) {
      return Padding(
        padding: const EdgeInsets.only(
          left: defaultPadding * 3,
          right: defaultPadding * 3,
          bottom: defaultPadding * 2,
        ),
        child: kIsWeb ? _buildWebPdfViewer() : _buildMobilePdfViewer(),
      );
    }

    return SizedBox(
      width: double.infinity,
      height: double.infinity,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.picture_as_pdf, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'No PDF Available',
              style: AppFonts.semiBoldTextStyle(18, color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Text(
              'Select a report to view its PDF',
              style: AppFonts.regularTextStyle(14, color: Colors.grey[500]),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // PDF Viewer Panel (Left side - takes most space)
          Expanded(
            flex: 3,
            child: Container(
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12),
                  bottomLeft: Radius.circular(12),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // PDF Title Header
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.only(
                      top: defaultPadding * 2,
                      left: defaultPadding * 2.3,
                      right: defaultPadding,
                      bottom: defaultPadding,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(12),
                      ),
                    ),
                    child: Row(
                      children: [
                        const SizedBox(width: 12),

                        // Title and Status
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                widget.report.title,
                                style: AppFonts.semiBoldTextStyle(
                                  25,
                                  color: AppTheme.primaryTextColor,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 4),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  // PDF Viewer Content
                  Expanded(
                    child: ClipRRect(
                      borderRadius: const BorderRadius.only(
                        bottomLeft: Radius.circular(12),
                      ),
                      child: _buildPdfViewerContent(),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Vertical divider
          Container(width: 1, color: Colors.grey[200]),

          // PDF Info Panel (Right side - fixed width)
          Container(
            width: ResponsiveSizes.reportSidebarWidth(context),
            decoration: const BoxDecoration(
              borderRadius: BorderRadius.only(
                topRight: Radius.circular(12),
                bottomRight: Radius.circular(12),
              ),
            ),
            child: _buildPdfInfoPanelContent(),
          ),
        ],
      ),
    );
  }
}

// Embedded PDF Viewer for Web
class _EmbeddedPDFViewerWeb extends StatefulWidget {
  final Uint8List fileBytes;
  final String fileName;

  const _EmbeddedPDFViewerWeb({
    required this.fileBytes,
    required this.fileName,
  });

  @override
  State<_EmbeddedPDFViewerWeb> createState() => _EmbeddedPDFViewerWebState();
}

class _EmbeddedPDFViewerWebState extends State<_EmbeddedPDFViewerWeb> {
  final String _viewerId =
      'embedded-pdf-viewer-${DateTime.now().millisecondsSinceEpoch}';
  late String _url;

  @override
  void initState() {
    super.initState();
    final blob = html.Blob([widget.fileBytes], 'application/pdf');
    _url = html.Url.createObjectUrlFromBlob(blob);
    _registerViewFactory();
  }

  @override
  void dispose() {
    html.Url.revokeObjectUrl(_url);
    super.dispose();
  }

  void _registerViewFactory() {
    ui_web.platformViewRegistry.registerViewFactory(_viewerId, (int viewId) {
      // Create a container div with white background
      final container = html.DivElement()
        ..style.width = '100%'
        ..style.height = '100%'
        ..style.backgroundColor = '#ffffff'
        ..style.overflow = 'hidden'
        ..style.position = 'relative'
        ..style.display = 'flex'
        ..style.justifyContent = 'center'
        ..style.alignItems = 'center';

      // Add custom CSS to ensure white background and remove black borders
      final style = html.StyleElement()
        ..text = '''
          iframe {
            background-color: #ffffff !important;
            border: none !important;
          }
          body {
            background-color: #ffffff !important;
            margin: 0 !important;
            padding: 0 !important;
          }
           /* Hide specific PDF.js toolbar buttons */
          #toolbarViewerLeft .splitToolbarButton:not(.pageNumber):not(.numPages),
          #openFile,
          #print,
          #download,
          #viewBookmark,
          #secondaryToolbarToggle,
          #presentationMode,
          #editorModeButtons,
          #toolbarViewerMiddle .splitToolbarButton:not(.zoomOut):not(.zoomIn),
          #scaleSelectContainer select option:not([value="auto"]):not([value="page-fit"]):not([value="page-width"]),
          .verticalToolbarSeparator,
          /* Hide hamburger menu and filename */
          #sidebarToggle,
          .toolbarField,
          #toolbarViewerLeft .splitToolbarButton.toggled,
          .findbar {
            display: none !important;
          }
          
          /* Keep only essential controls visible */
          #pageNumber,
          #numPages,
          .toolbarButton.zoomOut,
          .toolbarButton.zoomIn,
          #scaleSelectContainer {
            display: inline-block !important;
          }
          
          /* Style the toolbar to be more compact */
          #toolbarContainer {
            background-color: #f8f9fa !important;
            border-bottom: 1px solid #dee2e6 !important;
            height: 40px !important;
          }
          
          .toolbar {
            height: 40px !important;
            min-height: 40px !important;
          }
          
          .toolbarButton {
            height: 28px !important;
            width: 28px !important;
            margin: 2px !important;
          }
          
          #pageNumber {
            width: 50px !important;
            height: 24px !important;
            margin: 0 4px !important;
          }
          
          #scaleSelectContainer select {
            height: 24px !important;
            font-size: 12px !important;
          }
        ''';
      html.document.head?.append(style);

      final iframe = html.IFrameElement()
        ..src = '$_url#toolbar=1&navpanes=0&scrollbar=1&view=FitH'
        ..style.width = '100%'
        ..style.height = '100%'
        ..style.backgroundColor = '#ffffff'
        ..style.border = 'none'
        ..style.display = 'block'
        ..setAttribute('allowfullscreen', 'true');

      container.append(iframe);
      return container;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Stack(
        children: [
          // PDF Viewer
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Container(
              color: Colors.white,
              child: HtmlElementView(viewType: _viewerId),
            ),
          ),
        ],
      ),
    );
  }
}

// Embedded PDF Viewer for Mobile
class _EmbeddedPDFViewerMobile extends StatefulWidget {
  final String filePath;
  final String fileName;

  const _EmbeddedPDFViewerMobile({
    required this.filePath,
    required this.fileName,
  });

  @override
  State<_EmbeddedPDFViewerMobile> createState() =>
      _EmbeddedPDFViewerMobileState();
}

class _EmbeddedPDFViewerMobileState extends State<_EmbeddedPDFViewerMobile> {
  final Completer<PDFViewController> _controller =
      Completer<PDFViewController>();
  int? pages = 0;
  int currentPage = 1;
  bool isReady = false;
  String errorMessage = '';
  double _zoomLevel = 1.0;

  void _zoomIn() {
    setState(() {
      _zoomLevel = (_zoomLevel * 1.2).clamp(0.5, 3.0);
    });
    // Note: flutter_pdfview doesn't support programmatic zoom
    // The zoom level is displayed for user reference only
  }

  void _zoomOut() {
    setState(() {
      _zoomLevel = (_zoomLevel / 1.2).clamp(0.5, 3.0);
    });
    // Note: flutter_pdfview doesn't support programmatic zoom
    // The zoom level is displayed for user reference only
  }

  void _resetZoom() {
    setState(() {
      _zoomLevel = 1.0;
    });
    // Note: flutter_pdfview doesn't support programmatic zoom
    // The zoom level is displayed for user reference only
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Stack(
          children: [
            if (widget.filePath.isNotEmpty)
              PDFView(
                filePath: widget.filePath,
                enableSwipe: true,
                swipeHorizontal: false,
                autoSpacing: false,
                pageFling: false,
                backgroundColor: Colors.white,
                onRender: (pagesCount) {
                  setState(() {
                    pages = pagesCount;
                    isReady = true;
                  });
                },
                onError: (error) {
                  setState(() {
                    errorMessage = error.toString();
                  });
                  debugPrint('Embedded PDF Error: ${error.toString()}');
                },
                onPageError: (page, error) {
                  debugPrint(
                    'Embedded PDF Page Error - Page $page: ${error.toString()}',
                  );
                },
                onPageChanged: (int? page, int? total) {
                  setState(() {
                    currentPage = (page ?? 0) + 1;
                  });
                },
                onViewCreated: (PDFViewController pdfViewController) {
                  _controller.complete(pdfViewController);
                },
              )
            else
              Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.picture_as_pdf,
                      size: 64,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No PDF file path provided',
                      style: AppFonts.regularTextStyle(
                        16,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            if (!isReady && widget.filePath.isNotEmpty)
              Container(
                color: Colors.white.withValues(alpha: 0.8),
                child: const Center(child: CircularProgressIndicator()),
              ),
            if (errorMessage.isNotEmpty)
              Container(
                color: Colors.white,
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.error_outline,
                        size: 64,
                        color: Colors.red,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Error loading PDF',
                        style: AppFonts.semiBoldTextStyle(
                          18,
                          color: AppTheme.primaryTextColor,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 32),
                        child: Text(
                          errorMessage,
                          textAlign: TextAlign.center,
                          style: AppFonts.regularTextStyle(
                            14,
                            color: Colors.grey[600],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            // Page indicator overlay
            if (isReady && pages! > 1)
              Positioned(
                top: 16,
                right: 16,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.7),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    '$currentPage / $pages',
                    style: AppFonts.mediumTextStyle(12, color: Colors.white),
                  ),
                ),
              ),

            // Zoom Controls
            if (isReady)
              Positioned(
                top: 16,
                left: 16,
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: const Icon(Icons.zoom_in, size: 20),
                        onPressed: _zoomIn,
                        tooltip: 'Zoom In',
                        constraints: const BoxConstraints(
                          minWidth: 36,
                          minHeight: 36,
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: Text(
                          '${(_zoomLevel * 100).toInt()}%',
                          style: AppFonts.regularTextStyle(
                            10,
                            color: Colors.grey[700],
                          ),
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.zoom_out, size: 20),
                        onPressed: _zoomOut,
                        tooltip: 'Zoom Out',
                        constraints: const BoxConstraints(
                          minWidth: 36,
                          minHeight: 36,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.fit_screen, size: 20),
                        onPressed: _resetZoom,
                        tooltip: 'Fit to Screen',
                        constraints: const BoxConstraints(
                          minWidth: 36,
                          minHeight: 36,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
