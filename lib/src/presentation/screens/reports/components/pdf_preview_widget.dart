import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:neorevv/src/core/config/constants.dart';
import 'package:neorevv/src/core/theme/app_fonts.dart';
import 'package:neorevv/src/core/theme/app_theme.dart';
import 'package:neorevv/src/domain/models/report.dart';
import 'package:dio/dio.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';

// Web-specific imports (conditional)
// ignore: avoid_web_libraries_in_flutter, deprecated_member_use
import 'dart:html' as html;
// ignore: avoid_web_libraries_in_flutter
import 'dart:ui_web' as ui_web;

class PdfPreviewWidget extends StatefulWidget {
  final ReportModel report;

  const PdfPreviewWidget({
    super.key,
    required this.report,
  });

  @override
  State<PdfPreviewWidget> createState() => _PdfPreviewWidgetState();
}

class _PdfPreviewWidgetState extends State<PdfPreviewWidget> {
  bool isPdfLoaded = false;

  // PDF loading state
  bool isLoadingPdf = false;
  bool hasError = false;
  String errorMessage = '';
  Uint8List? pdfBytes;
  String? pdfFilePath;

  @override
  void initState() {
    super.initState();
    _loadPdf();
  }

  Future<void> _loadPdf() async {
    setState(() {
      isLoadingPdf = true;
      hasError = false;
      errorMessage = '';
      isPdfLoaded = false;
    });

    try {
      if (widget.report.url.isNotEmpty) {
        await _loadRemotePdf();
      } else {
        await _loadLocalPdf();
      }
      setState(() {
        isPdfLoaded = true;
      });
    } catch (e) {
      debugPrint('Error loading PDF: $e');
      setState(() {
        isLoadingPdf = false;
        hasError = true;
        errorMessage = 'Failed to load PDF: $e';
        isPdfLoaded = false;
      });
    }
  }

  Future<void> _loadRemotePdf() async {
    try {
      debugPrint('Loading PDF from URL: ${widget.report.url}');
      final dio = Dio();
      final response = await dio.get(
        widget.report.url,
        options: Options(responseType: ResponseType.bytes),
      );

      if (response.statusCode == 200 && response.data != null) {
        final bytes = Uint8List.fromList(response.data);

        // For mobile, save to temporary file
        if (!kIsWeb) {
          await _savePdfToTempFile(bytes);
        }

        setState(() {
          pdfBytes = bytes;
          isLoadingPdf = false;
        });
        debugPrint('PDF loaded successfully: ${pdfBytes!.length} bytes');
      } else {
        throw Exception('Failed to load PDF: HTTP ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error loading remote PDF: $e');
      // Fallback to local PDF
      await _loadLocalPdf();
    }
  }

  Future<void> _loadLocalPdf() async {
    try {
      debugPrint('Loading local PDF asset...');
      final ByteData data = await rootBundle.load('assets/pdfs/sample_report.pdf');
      final bytes = data.buffer.asUint8List();

      // For mobile, we need to save to a temporary file
      if (!kIsWeb) {
        await _savePdfToTempFile(bytes);
      }

      setState(() {
        pdfBytes = bytes;
        isLoadingPdf = false;
        hasError = false;
      });
      debugPrint('Local PDF loaded successfully: ${pdfBytes!.length} bytes');
    } catch (e) {
      debugPrint('Error loading local PDF: $e');
      setState(() {
        isLoadingPdf = false;
        hasError = true;
        errorMessage = 'Failed to load PDF. Both remote and local sources unavailable.';
      });
    }
  }

  Future<void> _savePdfToTempFile(Uint8List bytes) async {
    try {
      // For mobile platforms, we need to save the PDF to a temporary file
      // This is a simplified approach - in production you'd use path_provider
      final tempDir = '/tmp'; // This would be replaced with proper temp directory
      final fileName = 'temp_${widget.report.id}_${DateTime.now().millisecondsSinceEpoch}.pdf';
      pdfFilePath = '$tempDir/$fileName';

      debugPrint('PDF would be saved to: $pdfFilePath');
    } catch (e) {
      debugPrint('Error saving PDF to temp file: $e');
    }
  }

  void _handlePreview() {
    if (!isPdfLoaded) return;
    
    // Open PDF in full screen using the existing PDF viewers
    if (kIsWeb) {
      // For web, we could open in a new tab or modal
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Opening PDF in full screen...'),
          backgroundColor: Colors.blue,
        ),
      );
    } else {
      // For mobile, use the mobile PDF viewer
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Opening PDF in full screen...'),
          backgroundColor: Colors.blue,
        ),
      );
    }
  }

  void _handleDownload() {
    if (!isPdfLoaded) return;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.download, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'Downloading ${widget.report.title}.pdf...',
                style: AppFonts.regularTextStyle(14, color: Colors.white),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
        action: SnackBarAction(
          label: 'View',
          textColor: Colors.white,
          onPressed: () {
            // Open file location or viewer
          },
        ),
      ),
    );
  }

  Widget _buildWebPdfViewer() {
    if (pdfBytes == null) return const SizedBox();

    return _EmbeddedPDFViewerWeb(
      fileBytes: pdfBytes!,
      fileName: widget.report.title,
    );
  }

  Widget _buildMobilePdfViewer() {
    if (pdfBytes == null || pdfFilePath == null) {
      return Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.grey[100],
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.picture_as_pdf,
                size: 64,
                color: Colors.red[400],
              ),
              const SizedBox(height: 16),
              Text(
                'PDF Viewer',
                style: AppFonts.semiBoldTextStyle(18, color: Colors.grey[700]),
              ),
              const SizedBox(height: 8),
              Text(
                'Preparing PDF for mobile viewing...',
                style: AppFonts.regularTextStyle(14, color: Colors.grey[600]),
              ),
            ],
          ),
        ),
      );
    }

    return _EmbeddedPDFViewerMobile(
      filePath: pdfFilePath!,
      fileName: widget.report.title,
    );
  }

  Widget _buildPdfInfoPanelContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // PDF Icon and Title Section
          Container(
            padding: const EdgeInsets.all(defaultPadding),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // PDF Icon
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: Colors.red[50],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.picture_as_pdf,
                    size: 24,
                    color: Colors.red[600],
                  ),
                ),
                const SizedBox(height: 12),

                // PDF Title
                Text(
                  '${widget.report.title}.pdf',
                  style: AppFonts.semiBoldTextStyle(
                    16,
                    color: AppTheme.primaryTextColor,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),

                // PDF Status
                Row(
                  children: [
                    Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: isPdfLoaded ? Colors.green : Colors.orange,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      isPdfLoaded ? 'Ready' : 'Loading...',
                      style: AppFonts.regularTextStyle(
                        12,
                        color: isPdfLoaded ? Colors.green[700] : Colors.orange[700],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Action Buttons Section
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Preview Button
                  ElevatedButton.icon(
                    onPressed: isPdfLoaded ? _handlePreview : null,
                    icon: const Icon(Icons.visibility, size: 18),
                    label: Text(
                      'Preview',
                      style: AppFonts.mediumTextStyle(
                        14,
                        color: Colors.white,
                      ),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      elevation: 0,
                    ),
                  ),

                  const SizedBox(height: 12),

                  // Download Button
                  OutlinedButton.icon(
                    onPressed: isPdfLoaded ? _handleDownload : null,
                    icon: const Icon(Icons.download, size: 18),
                    label: Text(
                      'Download',
                      style: AppFonts.mediumTextStyle(
                        14,
                        color: Colors.grey[700],
                      ),
                    ),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.grey[700],
                      side: BorderSide(color: Colors.grey[300]!),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),

                  const SizedBox(height: 24),

                  // PDF Details
                  Container(
                    padding: const EdgeInsets.all(12),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Document Details',
                          style: AppFonts.semiBoldTextStyle(
                            12,
                            color: Colors.grey[700],
                          ),
                        ),
                        const SizedBox(height: 8),
                        _buildDetailRow('Type', 'PDF Document'),
                        _buildDetailRow('Category', 'Report'),
                        _buildDetailRow('Status', isPdfLoaded ? 'Available' : 'Loading'),
                      ],
                    ),
                  ),

                  const Spacer(),

                  // Help Text
                  Text(
                    'Click Preview to view the document in full screen or Download to save it to your device.',
                    style: AppFonts.regularTextStyle(
                      11,
                      color: Colors.grey[500],
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: AppFonts.regularTextStyle(11, color: Colors.grey[600]),
          ),
          Text(
            value,
            style: AppFonts.mediumTextStyle(11, color: Colors.grey[800]),
          ),
        ],
      ),
    );
  }

  Widget _buildPdfViewerContent() {
    if (isLoadingPdf) {
      return Container(
        width: double.infinity,
        height: double.infinity,
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Loading PDF...'),
            ],
          ),
        ),
      );
    }

    if (hasError) {
      return Container(
        width: double.infinity,
        height: double.infinity,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red[400],
              ),
              const SizedBox(height: 16),
              Text(
                'Error Loading PDF',
                style: AppFonts.semiBoldTextStyle(18, color: AppTheme.primaryTextColor),
              ),
              const SizedBox(height: 8),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 32),
                child: Text(
                  errorMessage,
                  textAlign: TextAlign.center,
                  style: AppFonts.regularTextStyle(14, color: Colors.grey[600]),
                ),
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _loadPdf,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                ),
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    if (pdfBytes != null) {
      return kIsWeb ? _buildWebPdfViewer() : _buildMobilePdfViewer();
    }

    return Container(
      width: double.infinity,
      height: double.infinity,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.picture_as_pdf,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No PDF Available',
              style: AppFonts.semiBoldTextStyle(18, color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Text(
              'Select a report to view its PDF',
              style: AppFonts.regularTextStyle(14, color: Colors.grey[500]),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // PDF Viewer Panel (Left side - takes most space)
          Expanded(
            flex: 3,
            child: Container(
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12),
                  bottomLeft: Radius.circular(12),
                ),
              ),
              child: ClipRRect(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  bottomLeft: Radius.circular(12),
                ),
                child: _buildPdfViewerContent(),
              ),
            ),
          ),

          // Vertical divider
          Container(
            width: 1,
            color: Colors.grey[200],
          ),

          // PDF Info Panel (Right side - fixed width)
          Container(
            width: 280,
            decoration: const BoxDecoration(
              borderRadius: BorderRadius.only(
                topRight: Radius.circular(12),
                bottomRight: Radius.circular(12),
              ),
            ),
            child: _buildPdfInfoPanelContent(),
          ),
        ],
      ),
    );
  }
}

// Embedded PDF Viewer for Web
class _EmbeddedPDFViewerWeb extends StatelessWidget {
  final Uint8List fileBytes;
  final String fileName;

  const _EmbeddedPDFViewerWeb({
    required this.fileBytes,
    required this.fileName,
  });

  @override
  Widget build(BuildContext context) {
    final blob = html.Blob([fileBytes], 'application/pdf');
    final url = html.Url.createObjectUrlFromBlob(blob);
    final String viewerId =
        'embedded-pdf-viewer-${DateTime.now().millisecondsSinceEpoch}';

    // Register the view factory
    ui_web.platformViewRegistry.registerViewFactory(viewerId, (int viewId) {
      // Create a container div with white background
      final container = html.DivElement()
        ..style.width = '100%'
        ..style.height = '100%'
        ..style.backgroundColor = 'white'
        ..style.borderRadius = '8px'
        ..style.overflow = 'hidden'
        ..style.position = 'relative';

      // Add custom CSS to hide PDF viewer chrome
      final style = html.StyleElement()
        ..text = '''
          iframe {
            background-color: white !important;
          }
          /* Hide PDF.js toolbar if it appears */
          .toolbar {
            display: none !important;
          }
          .toolbarViewer {
            display: none !important;
          }
          .secondaryToolbar {
            display: none !important;
          }
        ''';
      html.document.head?.append(style);

      final iframe = html.IFrameElement()
        ..src = '$url#toolbar=0&navpanes=0&scrollbar=0&view=FitH&zoom=page-fit'
        ..style.width = '100%'
        ..style.height = '100%'
        ..style.border = 'none'
        ..style.backgroundColor = 'white'
        ..style.display = 'block'
        ..setAttribute('allowfullscreen', 'true');

      container.append(iframe);
      return container;
    });

    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Container(
          color: Colors.white,
          child: HtmlElementView(viewType: viewerId),
        ),
      ),
    );
  }
}

// Embedded PDF Viewer for Mobile
class _EmbeddedPDFViewerMobile extends StatefulWidget {
  final String filePath;
  final String fileName;

  const _EmbeddedPDFViewerMobile({
    required this.filePath,
    required this.fileName,
  });

  @override
  State<_EmbeddedPDFViewerMobile> createState() => _EmbeddedPDFViewerMobileState();
}

class _EmbeddedPDFViewerMobileState extends State<_EmbeddedPDFViewerMobile> {
  int? pages = 0;
  int currentPage = 1;
  bool isReady = false;
  String errorMessage = '';

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Stack(
          children: [
            if (widget.filePath.isNotEmpty)
              PDFView(
                filePath: widget.filePath,
                enableSwipe: true,
                swipeHorizontal: false,
                autoSpacing: false,
                pageFling: false,
                backgroundColor: Colors.white,
                onRender: (pagesCount) {
                  setState(() {
                    pages = pagesCount;
                    isReady = true;
                  });
                },
                onError: (error) {
                  setState(() {
                    errorMessage = error.toString();
                  });
                  debugPrint('Embedded PDF Error: ${error.toString()}');
                },
                onPageError: (page, error) {
                  debugPrint('Embedded PDF Page Error - Page $page: ${error.toString()}');
                },
                onPageChanged: (int? page, int? total) {
                  setState(() {
                    currentPage = (page ?? 0) + 1;
                  });
                },
              )
            else
              Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.picture_as_pdf,
                      size: 64,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No PDF file path provided',
                      style: AppFonts.regularTextStyle(16, color: Colors.grey[600]),
                    ),
                  ],
                ),
              ),
            if (!isReady && widget.filePath.isNotEmpty)
              Container(
                color: Colors.white.withValues(alpha: 0.8),
                child: const Center(
                  child: CircularProgressIndicator(),
                ),
              ),
            if (errorMessage.isNotEmpty)
              Container(
                color: Colors.white,
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.error_outline,
                        size: 64,
                        color: Colors.red,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Error loading PDF',
                        style: AppFonts.semiBoldTextStyle(18, color: AppTheme.primaryTextColor),
                      ),
                      const SizedBox(height: 8),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 32),
                        child: Text(
                          errorMessage,
                          textAlign: TextAlign.center,
                          style: AppFonts.regularTextStyle(14, color: Colors.grey[600]),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            // Page indicator overlay
            if (isReady && pages! > 1)
              Positioned(
                top: 16,
                right: 16,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.7),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    '$currentPage / $pages',
                    style: AppFonts.mediumTextStyle(12, color: Colors.white),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
