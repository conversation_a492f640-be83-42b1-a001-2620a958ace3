import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:neorevv/src/core/config/constants.dart';
import 'package:neorevv/src/core/theme/app_fonts.dart';
import 'package:neorevv/src/domain/models/report.dart';
import 'pdf_viewer_panel.dart';
import 'pdf_info_panel.dart';

class PdfPreviewWidget extends StatefulWidget {
  final ReportModel report;

  const PdfPreviewWidget({
    super.key,
    required this.report,
  });

  @override
  State<PdfPreviewWidget> createState() => _PdfPreviewWidgetState();
}

class _PdfPreviewWidgetState extends State<PdfPreviewWidget> {
  bool isPdfLoaded = false;

  void _handlePdfLoadStateChanged(bool isLoaded) {
    setState(() {
      isPdfLoaded = isLoaded;
    });
  }

  void _handlePreview() {
    if (!isPdfLoaded) return;
    
    // Open PDF in full screen using the existing PDF viewers
    if (kIsWeb) {
      // For web, we could open in a new tab or modal
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Opening PDF in full screen...'),
          backgroundColor: Colors.blue,
        ),
      );
    } else {
      // For mobile, use the mobile PDF viewer
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Opening PDF in full screen...'),
          backgroundColor: Colors.blue,
        ),
      );
    }
  }

  void _handleDownload() {
    if (!isPdfLoaded) return;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.download, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'Downloading ${widget.report.title}.pdf...',
                style: AppFonts.regularTextStyle(14, color: Colors.white),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
        action: SnackBarAction(
          label: 'View',
          textColor: Colors.white,
          onPressed: () {
            // Open file location or viewer
          },
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // PDF Viewer Panel (Left side - takes most space)
        Expanded(
          flex: 3,
          child: PdfViewerPanel(
            report: widget.report,
            onPdfLoadStateChanged: _handlePdfLoadStateChanged,
          ),
        ),
        
        const SizedBox(width: defaultPadding),
        
        // PDF Info Panel (Right side - fixed width)
        PdfInfoPanel(
          report: widget.report,
          isPdfLoaded: isPdfLoaded,
          onPreview: _handlePreview,
          onDownload: _handleDownload,
        ),
      ],
    );
  }
}
