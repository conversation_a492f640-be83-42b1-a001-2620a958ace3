import 'package:flutter/material.dart';
import 'package:neorevv/src/core/config/constants.dart';
import 'package:neorevv/src/core/theme/app_fonts.dart';
import 'package:neorevv/src/core/theme/app_theme.dart';
import 'package:neorevv/src/domain/models/report.dart';

class PdfInfoPanel extends StatelessWidget {
  final ReportModel report;
  final VoidCallback? onPreview;
  final VoidCallback? onDownload;
  final bool isPdfLoaded;

  const PdfInfoPanel({
    super.key,
    required this.report,
    this.onPreview,
    this.onDownload,
    this.isPdfLoaded = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 280,
      height: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // PDF Icon and Title Section
          Container(
            padding: const EdgeInsets.all(defaultPadding),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 12),

                // PDF Title
                Text(
                  '${report.title}.pdf',
                  style: AppFonts.semiBoldTextStyle(
                    16,
                    color: AppTheme.primaryTextColor,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),

          // Action Buttons Section
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Preview Button
                  ElevatedButton.icon(
                    onPressed: isPdfLoaded ? onPreview : null,
                    icon: const Icon(Icons.visibility, size: 18),
                    label: Text(
                      'Preview',
                      style: AppFonts.mediumTextStyle(14, color: Colors.white),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      elevation: 0,
                    ),
                  ),

                  const SizedBox(height: 12),

                  // Download Button
                  OutlinedButton.icon(
                    onPressed: isPdfLoaded ? onDownload : null,
                    icon: const Icon(Icons.download, size: 18),
                    label: Text(
                      'Download',
                      style: AppFonts.mediumTextStyle(
                        14,
                        color: Colors.grey[700],
                      ),
                    ),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.grey[700],
                      side: BorderSide(color: Colors.grey[300]!),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
